<?php
$size = 'full'; // Dit lijkt overbodig als je 'size_text' gebruikt.
$logo = get_field("logo");
$video = get_field("video");
$image = get_field("image");
?>
<section class="imageQuoteBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper small">
        <div class="col">
            <span class="backgroundWrapper"><span class="background" data-parallax data-parallax-speed="-8"></span></span>
            <div class="imageWrapper">
                <div class="innerImage">
                    <?php if ($video): ?>
                        <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
                            <source src="<?php echo esc_url($video); ?>" type="video/mp4">
                        </video>
                    <?php elseif ($image): ?>
                        <?php
                        $image = optimize_images_for_compressx($image);
                        ?>
                        <img class="lazy"
                             data-src="<?php echo esc_url($image["sizes"]['large']); ?>"
                             alt="<?php echo esc_attr($image['alt']); ?>"
                             width="<?php echo esc_attr($image['sizes']['large-width'] ?? $image['width'] ?? ''); ?>"
                             height="<?php echo esc_attr($image['sizes']['large-height'] ?? $image['height'] ?? ''); ?>" />
                    <?php endif; ?>
                </div>
            </div>
            <div class="titleWrapper"data-parallax data-parallax-speed="2">
                <?php if(get_field("subtitle")): ?><div class="tinyTitle" data-lines data-words><?php the_field("subtitle"); ?></div><?php endif; ?>
                <h3 class="mediumTitle" data-lines data-words><?php the_field("text"); ?></h3>
                <div class="buttonWrapper">
                    <?php render_text_link('button'); ?>
                    <?php render_text_link('button_2'); ?>
                </div>
            </div>
        </div>
    </div>
</section>
