<?php
$size = 'medium_large'; 
$subtitle = get_field('subtitle');
$buttons = get_field('buttons');
$quick_artists_title = get_field('quick_artists_title');
$artists_amount = get_field('artists_amount');
$hide_quick_links = get_field('hide_quick_links');
$video = get_field('background_video');
$image = get_field('background_image');
$bottom_image = get_field('bottom_image');
$show_ones_to_watch = get_field('ones_to_watch');
$artists = new WP_Query([
  'post_type' => 'artist',
  'posts_per_page' => 8,
  'post_status'    => 'publish',
  'orderby' => 'title',
  'order' => 'ASC',
  'meta_query' => [
    [
      'key' => 'image',
      'compare' => 'EXISTS',
    ],
    [
      'key' => 'roster_type',
      'value' => $show_ones_to_watch ? 'ones_to_watch' : 'main',
      'compare' => '=',
    ],
  ],
]);
?>
<section class="artistSliderBlock whiteBackground<?php if (get_field("padding_bottom")): ?> paddingBottom<?php endif;?>" data-show-cursor data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
   <?php if (get_field("show_logo")): ?>
      <svg viewBox="0 0 551.11 412.45">
        <path d="M68.04,294.26c1.29-2.34,2.34-4.35,3.48-6.32C125.77,193.96,180.06,100,234.18,5.94,236.75,1.47,239.52-.08,244.65,0c19.82.34,39.64.14,60.6.14-45.54,78.89-90.69,157.12-136.31,236.15h6.75c89.65,0,179.29.03,268.94-.1,4.04,0,6.2,1.26,8.15,4.73,9.05,16.11,18.38,32.05,27.59,48.06.89,1.55,1.7,3.14,2.85,5.27H68.04Z"/>
        <path d="M427.49,197.36c-22.34,0-43.65.05-64.95-.12-1.41-.01-3.38-1.37-4.13-2.66-19.73-33.94-39.34-67.94-58.88-101.99-.68-1.19-.98-3.33-.37-4.41,10.59-18.65,21.35-37.2,32.58-56.64,31.97,55.37,63.59,110.13,95.75,165.81Z"/>
        <path d="M111.98,412.36c3.35-5.85,6.22-10.86,9.11-15.87,11.39-19.75,22.74-39.52,34.28-59.18.99-1.68,3.29-3.65,5-3.67,21.15-.25,42.3-.16,64.06-.16-.65,1.56-1.01,2.77-1.63,3.83-13.8,23.93-27.59,47.86-41.52,71.72-.84,1.45-2.8,3.18-4.25,3.19-21.31.21-42.63.14-65.05.14Z"/>
        <path d="M0,412.06c3.94-6.89,7.61-13.36,11.32-19.79,10.48-18.16,21.06-36.25,31.39-54.5,1.78-3.15,3.66-4.42,7.34-4.39,20.48.18,40.96.09,62.42.09-1.03,2.09-1.71,3.66-2.56,5.13-13.46,23.35-26.96,46.68-40.4,70.05-1.3,2.26-2.48,3.83-5.63,3.8-20.32-.18-40.64-.09-60.96-.1-.78,0-1.55-.15-2.92-.29Z"/>
        <path d="M326.95,333.48c22.04,0,43.35-.04,64.65.1,1.31,0,3.15,1.2,3.83,2.37,14.4,24.73,28.67,49.54,42.95,74.34.23.4.26.9.51,1.8-1.43.1-2.67.27-3.91.27-19.66.02-39.31.09-58.96-.11-1.88-.02-4.59-1.3-5.49-2.83-14.32-24.39-28.41-48.92-42.54-73.42-.31-.54-.48-1.16-1.03-2.51Z"/>
        <path d="M551.11,412.36c-9.69,0-18.82,0-27.95,0-11.83,0-23.66.11-35.48-.12-1.75-.03-4.25-1.24-5.09-2.67-14.46-24.7-28.71-49.51-43-74.31-.16-.27-.16-.63-.33-1.38,1.27-.14,2.49-.39,3.71-.39,19.66-.02,39.31-.09,58.97.1,1.92.02,4.72,1.16,5.6,2.67,14.4,24.54,28.55,49.22,42.75,73.87.24.41.34.9.82,2.23Z"/>
        <path d="M276.16,127.69c13.21,23.51,25.96,46.17,39,69.37h-78.79c13.25-23.11,26.25-45.76,39.79-69.37Z"/>
      </svg>
    <?php endif; ?> 
  <div class="contentWrapper">
    <div class="sliderWrapper">
      <div class="topWrapper">
        <div class="col">
          <div class="sliderButton arrowButton prev primary" data-prev><i class="icon-chevron-left"></i><i class="icon-chevron-left"></i></div>
          <div class="sliderButton arrowButton next primary" data-next><i class="icon-chevron-right"></i><i class="icon-chevron-right"></i></div>
        </div>
        <div class="col">
        </div>
      </div>
      <div class="artistSlider slider" data-slider>
        <?php 
        $button = get_field('button'); 
        $has_button = ($button && isset($button['url'], $button['title']));
        ?>
        <?php if ($has_button): ?>
          <a class="slide partial intro<?php if($show_ones_to_watch):?> primary<?php endif; ?>" href="<?php echo esc_url($button['url']); ?>" title="<?php echo esc_attr($button['title']); ?>">
        <?php else: ?>
          <div class="slide partial intro<?php if($show_ones_to_watch):?> primary<?php endif; ?>">
        <?php endif; ?>
            <div class="tinyTitle smaller"><?php the_field("subtitle"); ?></div>
            <h2 class="normalTitle"><?php the_field("title"); ?></h2>
            <div class="text"><p><?php the_field("text"); ?></p></div>
            <?php if ($has_button): ?>
              <span class="button<?php if (!$show_ones_to_watch) echo ' primary'; ?>">
                <span class="innerText"><?php echo esc_html($button['title']); ?></span>
                <span class="arrows">
                  <i class="icon-arrow-right-up"></i>
                  <i class="icon-arrow-right-up"></i>
                </span>
              </span>
            <?php endif; ?>
        <?php if ($has_button): ?>
          </a>
        <?php else: ?>
          </div>
        <?php endif; ?>
        <?php if( $artists->have_posts() ): ?>
            <?php foreach ( $artists->posts as $post ) :
              setup_postdata($post);
              $title = get_the_title($post->ID);
              $link = get_the_permalink($post->ID);
              $image = get_field('image', $post->ID);
              ?>
              <a class="slide artist partial" title="<?php echo esc_html( $title ); ?>" href="<?php echo esc_url($link); ?>">
                <?php if ($image && isset($image['sizes'][$size])): ?>
                  <?php
                  $image = optimize_images_for_compressx($image);
                  ?>
                  <img class="lazy"
                       data-src="<?php echo esc_url($image['sizes'][$size]); ?>"
                       alt="<?php echo esc_attr($image['alt'] ?? $title); ?>"
                       width="<?php echo esc_attr($image['sizes'][$size . '-width'] ?? $image['width'] ?? ''); ?>"
                       height="<?php echo esc_attr($image['sizes'][$size . '-height'] ?? $image['height'] ?? ''); ?>"/>
                <?php endif; ?>
                <span class="smallTitle"><?php echo esc_html( $title ); ?></span>
                <span class="arrows"><i class="icon-arrow-right-up"></i><i class="icon-arrow-right-up"></i></span>
              </a>
            <?php endforeach; ?>
            <?php wp_reset_postdata(); ?>
        <?php endif; ?>
      </div>
    </div>
  </div>
</section>