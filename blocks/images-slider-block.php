<?php
$size = 'large'; // Image size for WordPress
?>
<section class="imagesSliderBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
  <div class="contentWrapper small">
    <div class="innerContent">
      <h2 class="bigTitle splitThis" data-init data-split><?php the_field('title'); ?></h2>
      <div class="text grey">
        <?php the_field('text'); ?>
      </div>
    </div>
    <div class="sliderWrapper">
      <div class="slider" data-slider>
        <?php if( have_rows('slide') ): // Check if repeater has rows ?>
          <?php while( have_rows('slide') ): the_row(); 
            $image = get_sub_field('image');
            $video = get_sub_field('video');
          ?>
            <div class="slide">
              <div class="imageWrapper">
                <div class="innerImage">
                <?php if ($video): ?>
                  <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
                      <source src="<?php echo esc_url($video); ?>" type="video/mp4">
                  </video>
                  <?php elseif ($image): ?>
                      <?php
                      $image = optimize_images_for_compressx($image);
                      ?>
                      <img class="lazy"
                           data-src="<?php echo esc_url($image['sizes'][$size]); ?>"
                           alt="<?php echo esc_attr($image['alt']); ?>"
                           width="<?php echo esc_attr($image['sizes'][$size . '-width'] ?? $image['width'] ?? ''); ?>"
                           height="<?php echo esc_attr($image['sizes'][$size . '-height'] ?? $image['height'] ?? ''); ?>"/>
                  <?php endif; ?>
                </div>
              </div>
            </div>
          <?php endwhile; ?>
        <?php else: ?>
          <p>Er zijn momenteel geen slides beschikbaar.</p>
        <?php endif; ?>
      </div>
      <div class="sliderButton prev" data-prev><span class="circle"><span class="innerCircle"></span></span><i class="icon-arrow-left"></i></div>
      <div class="sliderButton next" data-next><span class="circle"><span class="innerCircle"></span></span><i class="icon-arrow-right"></i></div>
      <div class="sliderIndicator"><div class="innerBar"></div></div>
    </div>
  </div>
</section>
