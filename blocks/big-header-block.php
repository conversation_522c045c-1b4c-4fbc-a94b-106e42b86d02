<?php
  $size = 'full';
  $video = get_field("background_video");
  $image = get_field("background_image");
  $image1 = get_field("image_1");
  $image2 = get_field("image_2");
  $youtube_link = get_field("youtube_link");

  // Extract YouTube video ID from URL if needed
  $youtube_video_id = '';
  if ($youtube_link) {
      // If it's already just a video ID (11 characters), use it directly
      if (preg_match('/^[a-zA-Z0-9_-]{11}$/', $youtube_link)) {
          $youtube_video_id = $youtube_link;
      } else {
          // Extract video ID from full YouTube URL
          $video_patterns = [
              // Standard youtube.com/watch?v= format with optional parameters
              '/youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/',
              // youtu.be/ format with optional parameters (like ?si=)
              '/youtu\.be\/([a-zA-Z0-9_-]{11})(?:\?.*)?/',
              // youtube.com/embed/ format
              '/youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/',
              // youtube.com/v/ format (legacy)
              '/youtube\.com\/v\/([a-zA-Z0-9_-]{11})/',
          ];

          foreach ($video_patterns as $pattern) {
              if (preg_match($pattern, $youtube_link, $matches)) {
                  $youtube_video_id = $matches[1];
                  break;
              }
          }
      }
  }
  ?>
<section class="bigHeaderBlock whiteBackground" data-show-cursor data-init <?php if (get_field("anchor")){ echo 'data-anchor="' . esc_attr(get_field("anchor")) . '"'; } ?>>
    <div class="contentWrapper">
        <div class="topCols">
          <div class="col">
              <?php if (get_field("title")){ ?><h1 class="hugeTitle" data-lines data-words><?php the_field("title"); ?></h1><?php } ?>
          </div>
          <div class="col">
              <?php if (get_field("top_text")){ ?><h2 class="normalTitle" data-lines data-words><?php the_field("top_text"); ?></h2><?php } ?>
          </div>
        </div>
    </div>
    <div class="bigMediaWrapper blackBackground">
        <div class="background" data-parallax data-parallax-speed="2">
            <?php if ($video): ?>
                <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
                    <source src="<?php echo esc_url($video); ?>" type="video/mp4">
                </video>
            <?php elseif ($image): ?>
                <?php
                $image = add_image_dimensions($image);
                $webp_url = get_webp_image_url($image["sizes"]['large']);
                ?>
                <img class="lazy"
                     data-src="<?php echo esc_url($webp_url); ?>"
                     alt="<?php echo esc_attr($image['alt']); ?>"
                     width="<?php echo esc_attr($image['sizes']['large-width'] ?? $image['width'] ?? ''); ?>"
                     height="<?php echo esc_attr($image['sizes']['large-height'] ?? $image['height'] ?? ''); ?>" />
            <?php endif; ?>
        </div>
        <div class="contentWrapper">
            <div class="innerWrapper">
            <h3 class="subTitle white" data-lines data-words><?php the_field("subtitle"); ?></h3>
            <div class="introTextWrapper" data-anim-text>
                <div class="normalTitle white" data-lines><?php the_field("text"); ?></div>
                <div class="normalTitle white overlayText" data-lines aria-hidden="true"><?php the_field("text"); ?></div>
            </div>
            <?php if ($youtube_video_id): ?>
                <div class="youtubeContainer">
                    <div class="videoWrapper">
                        <iframe
                            src="https://www.youtube.com/embed/<?php echo esc_attr($youtube_video_id); ?>?rel=0&showinfo=0&modestbranding=1&fs=0&autoplay=1&mute=0&controls=1&disablekb=0&enablejsapi=1&iv_load_policy=3&loop=0&origin=<?php echo esc_url(home_url()); ?>"
                            frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                            loading="lazy"
                            title="YouTube Video"
                            referrerpolicy="strict-origin-when-cross-origin">
                        </iframe>
                    </div>
                </div>
            <?php endif; ?>
            <?php if (get_field("extra_text")){ ?>
            <div class="extraText text white">
            <p><?php the_field("extra_text"); ?></p>
            </div>
            <?php } ?>
            </div>
        </div>
    </div>
    <?php if ($image1 && $image2): ?>
    <div class="contentWrapper">
        <div class="images" data-parallax data-parallax-speed="4">
            <div class="imageWrapper">
                <div class="innerImage">
                    <?php
                    $image1 = add_image_dimensions($image1);
                    $image1_webp_url = get_webp_image_url($image1['sizes']['large']);
                    ?>
                    <img class="lazy"
                         data-src="<?php echo esc_url($image1_webp_url); ?>"
                         alt="<?php echo esc_attr($image1['alt']); ?>"
                         width="<?php echo esc_attr($image1['sizes']['large-width'] ?? $image1['width'] ?? ''); ?>"
                         height="<?php echo esc_attr($image1['sizes']['large-height'] ?? $image1['height'] ?? ''); ?>" />
                </div>
            </div>
            <div class="imageWrapper" data-parallax data-parallax-speed="-2">
                <div class="innerImage">
                    <?php
                    $image2 = add_image_dimensions($image2);
                    $image2_webp_url = get_webp_image_url($image2['sizes']['large']);
                    ?>
                    <img class="lazy"
                         data-src="<?php echo esc_url($image2_webp_url); ?>"
                         alt="<?php echo esc_attr($image2['alt']); ?>"
                         width="<?php echo esc_attr($image2['sizes']['large-width'] ?? $image2['width'] ?? ''); ?>"
                         height="<?php echo esc_attr($image2['sizes']['large-height'] ?? $image2['height'] ?? ''); ?>" />
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</section>
