var pageContainerWrap;
var scroller;
var scrollerHeight = 0;
var currentScrollY = 0;
var scrollValues = {};
var dynamicScripts = [];
var popState = false;
var resizeFunction;
var inlineStyles = null;

$(document).ready(function(){
    
    if ("ontouchstart" in document.documentElement){
        $("html").addClass("touch");
    }

    if ('scrollRestoration' in history) {
        history.scrollRestoration = 'manual';
    }

    updateDynamicScriptsArray();
    
    document.addEventListener('wpcf7invalid', function (event) {
        setTimeout(function(){
            $('.wpcf7-form-control').each(function () {
                if ($(this).hasClass('wpcf7-not-valid')) {
                    $(this).closest(".field").addClass('invalid');
                } else { 
                    $(this).closest(".field").removeClass('invalid');
                }
            });
        }, 10);
    }, false);


    pageContainerWrap = new Swup({
        cache:true,
        containers: ["#pageContainer"],
        animateHistoryBrowsing: true,
        linkSelector: 'a[href]:not(.hashtagLink):not([data-no-swup]):not([href^="#"]):not([href*="#"])',
        plugins: [new SwupHeadPlugin({
        persistAssets: true,
        ignoreVisit: (url, { el } = {}) => el?.closest('[data-no-swup]') || el?.classList.contains('hashtagLink') || el?.getAttribute('href')?.includes('#'),
        persistTags: 'style link',
        })]
        // , new SwupGaPlugin({
        //     gaMeasurementId: 'G-EWXT780YLB',
        // })]
    });
    
    
    $(document).off("click.hashtagLink", ".hashtagLink").on("click.hashtagLink", ".hashtagLink", function (e) {
        e.preventDefault(); // Prevent default link behavior

        var href = $(this).attr('href');
        var hash = href.replace('#', '').replace('/', '');

        // Check if this is a same-page hash link or cross-page hash link
        var currentPath = window.location.pathname;
        var linkPath = href.split('#')[0] || currentPath;

        if (linkPath !== currentPath && linkPath !== '') {
            // Cross-page hash link - navigate to the page with hash
            window.location.href = href;
            return;
        }

        // Same-page hash link - scroll to anchor
        if($("[data-anchor='" + hash + "']").length > 0){
            if(hash == "home"){
                scroller.scrollTo(0, {force:true});
            } else {
                scroller.scrollTo($("[data-anchor='" + hash + "']").offset().top - $("header .headerBar").height(), {force:true});
            }
            // Update the URL hash naturally
            window.location.hash = hash;
        }
    });
    
    pageContainerWrap.hooks.on('link:click', () => {
        scrollValues[window.location.href] = window.scrollY;
    });

    pageContainerWrap.hooks.on('history:popstate', () => {
        popState = true;
        $(document).on("initPage", function(){
            if(popState){
                window.scrollTo(0, scrollValues[window.location.href]);
                popState = false;
            }
        });
    });
    
    containerWidth = $(window).width();
    
    preloadPage();
    
    pageContainerWrap.hooks.before('content:replace', () => {
        inlineStyles = $("head style");
        $(".cmplz-optin").find("#cmplz-cookiebanner-container").hide();
        $(".cmplz-optin").find(".cmplz-cookiebanner").hide();
        $(".cmplz-optin").find(".cmplz-cookiebanner").css("opacity", "0");
        //Store the inline styles from th head, so they can be put back when in pageView event
    });
    
    
    pageContainerWrap.hooks.on('page:view', () => {
        dynamicScriptLoad();
        updateDynamicScriptsArray();
        if (inlineStyles) {
            $("head").append($(inlineStyles));
            inlineStyles = null;
        }
        setTimeout(function(){
            initPage();
            
        }, 100);
    });
    
    pageContainerWrap.hooks.on('animation:out:end', () => {
        scroller.scrollTo(0,{offset: 0, duration:0, easing: "linear", immediate: true});
        $("header").removeClass("scrollDown");
        scroller.stop();
        scroller.start();
        $("html").addClass("stopScroll");
    });
    
    pageContainerWrap.hooks.before('content:replace', () => {
        $("html").addClass("stopScroll");
    });
    
    
});

function updateDynamicScriptsArray(){
    $("head script").each(function(i, el){
        if($.inArray($(el).attr("src"), dynamicScripts) == -1){
            dynamicScripts.push($(el).attr("src"));
        }
    });
}

function dynamicScriptLoad(){
    // Herlaad externe scripts indien nodig
    $("head script").each(function(i, el){
        if($.inArray($(el).attr("src"), dynamicScripts) == -1){
            let scriptEle = document.createElement("script");
            scriptEle.setAttribute("src", $(el).attr("src"));
            $(el).remove();
            document.head.appendChild(scriptEle);
        }
    });
    // Voer alleen inline scripts uit die bedoeld zijn als JavaScript
    var container = $("#pageContainer")[0];
    var arr = container.getElementsByTagName('script');
    for (var n = 0; n < arr.length; n++){
        var scriptType = arr[n].getAttribute('type');
        // Alleen uitvoeren als type ontbreekt of type text/javascript is
        if(!scriptType || scriptType === 'text/javascript') {
            var scriptContent = arr[n].innerHTML.trim();
            if(scriptContent) {
                try {
                    new Function(scriptContent)();
                } catch(e) {
                    console.error('Fout in inline script:', scriptContent, e);
                }
            }
        }
    }
}

function initLenis(){
    scroller = new Lenis({
        easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // https://www.desmos.com/calculator/brs54l4xou
    });
    scroller.stop();
    scroller.on("scroll", function(e){
        $(document).trigger("scrollTrigger", currentScrollY);
    });
}

function raf(time) {
    scroller.raf(time);
    ScrollTrigger.update();
    requestAnimationFrame(raf);
}

function preloadPage(){
    initLenis();
    initPage();
    currentScrollY = $(window).scrollTop();
    scroller.on("scroll", function(e){
        currentScrollY = $(window).scrollTop();
    });

    requestAnimationFrame(raf);
    
    setTimeout(function(){
        $(".content").removeClass("fade");
        $("header").addClass("active");
    }, 300);
    
    
}

function initPage(){
    $("a[href*=\\#]").addClass("hashtagLink");
    $("html").removeClass("stopScroll fade");

    if(window.location.hash){
        var hash = window.location.hash.replace('#', '').replace('/', '');
        if($("[data-anchor='" + hash + "']").length > 0){
            scroller.scrollTo($("[data-anchor='" + hash + "']").offset().top - $("header").height(), {lock: 1, force:1});
            // Don't modify the URL - let the hash stay naturally
        }
    }
    if($(".instagramBlock").length > 0){
        sbi_init(); 
    }

    pageContainerWrap.hooks.on('page:view', () => {
        setTimeout(function(){
            if(window.location.hash){
                var hash = window.location.hash.replace('#', '').replace('/', '');
                if($("[data-anchor='" + hash + "']").length > 0){
                    scroller.scrollTo($("[data-anchor='" + hash + "']").offset().top - $("header .headerBar").height(), {lock: 1, force:1});
                    // Don't modify the URL - let the hash stay naturally
                }
            }
        }, 150);
    });
    
    
    setTimeout(function(){
        $("html, body").removeClass("overflow");
        scroller.start();
        $(document).trigger("initPage"); 
        checkInviewClasses();
        $(".cmplz-optin").find("#cmplz-cookiebanner-container").hide();
        $(".cmplz-optin").find(".cmplz-cookiebanner").hide();
        $(".cmplz-optin").find(".cmplz-cookiebanner").css("opacity", "0");

        setTimeout(function() {
            if ($(".cmplz-optin").length > 0) {
                show_cookie_banner();
                if ($(".cmplz-cookiebanner.cmplz-dismissed").length === 0) {
                    $(".cmplz-optin").find("#cmplz-cookiebanner-container").show();
                    $(".cmplz-optin").find("#cmplz-cookiebanner-container").css("opacity", "1");
                    $(".cmplz-optin").find(".cmplz-cookiebanner").show();
                    $(".cmplz-optin").find(".cmplz-cookiebanner").css("opacity", "1");
                }
            }
        }, 450);
    }, 600);
    

    lazyLoadImages();
    setTimeout(function() {
        checkVideos();
    }, 100);

    if (!$(".contactBlock").length > 0) {
        $(".flatpickr-calendar").remove();
    }

    
}

function lazyLoadImages() {
    var lazyloadImages;
    if ("IntersectionObserver" in window) {
        lazyloadImages = document.querySelectorAll(".lazy");

        // First, check for images that are already loaded (cached)
        lazyloadImages.forEach(function(image) {
            if (image.complete && image.naturalHeight !== 0) {
                // Image is already loaded, show it immediately
                image.classList.remove("lazy");
                image.classList.add("loaded");
                image.style.opacity = '1';
            }
        });

        // Re-select only images that still need lazy loading
        lazyloadImages = document.querySelectorAll(".lazy");
        const config = {
            root: null,
            rootMargin: '200px', // Reduced from 500px for better performance
            threshold: 0.1 // Increased threshold for better performance
        };
        var imageObserver = new IntersectionObserver(function (entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    var image = entry.target;

                    // Ensure image starts invisible (CSS should handle this, but double-check)
                    if (!image.style.opacity) {
                        image.style.opacity = '0';
                    }

                    // Create a new image to preload
                    var tempImage = new Image();

                    // Set up load handlers before setting src
                    tempImage.onload = function() {
                        // Image is fully loaded, now show it
                        image.src = image.dataset.src;
                        image.classList.remove("lazy");
                        image.classList.add("loaded");

                        // Let CSS handle the transition - don't override with inline styles
                        // The .loaded class will trigger the CSS transition
                    };

                    tempImage.onerror = function() {
                        // Handle error case - still show the image but log warning
                        image.src = image.dataset.src;
                        image.classList.remove("lazy");
                        image.classList.add("loaded");
                        console.warn('Failed to preload image:', image.dataset.src);
                    };

                    // Start preloading
                    tempImage.src = image.dataset.src;

                    imageObserver.unobserve(image);
                }
            });
        }, config);

        lazyloadImages.forEach(function(image) {
            imageObserver.observe(image);
        });
    } else {
        // Fallback for browsers without IntersectionObserver
        document.querySelectorAll(".lazy").forEach(function(image) {
            var tempImage = new Image();
            tempImage.onload = function() {
                image.src = image.dataset.src;
                image.classList.remove("lazy");
                image.classList.add("loaded");
            };

            tempImage.onerror = function() {
                image.src = image.dataset.src;
                image.classList.remove("lazy");
                image.classList.add("loaded");
            };

            tempImage.src = image.dataset.src;
        });
    }
}

function checkVideos() {
  const videos = document.querySelectorAll("video:not(.isStickyVideo)");
  videos.forEach(video => {
    ScrollTrigger.create({
      trigger: video,
      start: "top bottom",
      end: "bottom top",
      onEnter: () => video.play().catch(() => {}),
      onEnterBack: () => video.play().catch(() => {}),
      onLeave: () => video.pause(),
      onLeaveBack: () => video.pause(),
      markers: false
    });
  });
}

  function checkInviewClasses() {
    $("[data-init]").each(function() {
      const scrollDirect = $(this).data("scroll-direct");
      ScrollTrigger.create({
        trigger: this,
        start: scrollDirect ? "0% 100%" : "0% 90%",
        end: "0% 90%",
        once: true,
        onEnter: () => {
          if ($(this).get(0).hasAttribute("data-init-delay") && !$(this).hasClass("inview")) {
            var item = $(this);
            setTimeout(function() {
              $(item).addClass("inview");
            }, $(this).data("init-delay"));
          } else {
            $(this).addClass("inview");
          }
        }
      });
    });
  }