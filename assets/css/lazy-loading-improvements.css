/* Lazy Loading Improvements - Prevent border/flicker */

/* Base lazy loading styles */
img.lazy {
  opacity: 0;
  transition: opacity 0.4s ease-in-out, transform 0.4s ease-in-out;
  background-color: transparent;
  border: none;
  outline: none;
  /* Prevent any default browser styling that could cause borders */
  box-shadow: none;
  /* Ensure smooth rendering */
  image-rendering: auto;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* Loaded state */
img.lazy.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Prevent layout shift during image loading */
img[width][height] {
  height: auto;
  /* Maintain aspect ratio */
  max-width: 100%;
}

/* Specific improvements for bottom image */
.bottomImage.lazy {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
  /* Prevent any border artifacts */
  border: none;
  outline: none;
  box-shadow: none;
}

.bottomImage.lazy.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* About block image improvements */
.aboutBlock img.lazy {
  opacity: 0;
  transform: scale(1.05);
  transition: opacity 0.4s ease-in-out, transform 0.4s ease-in-out;
}

.aboutBlock img.lazy.loaded {
  opacity: 1;
  transform: scale(1);
}

/* Artist images improvements */
.artistHeaderBlock img,
.artistBigSliderBlock img.lazy {
  border: none;
  outline: none;
  box-shadow: none;
}

.artistBigSliderBlock img.lazy {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.artistBigSliderBlock img.lazy.loaded {
  opacity: 1;
}

/* Image blocks improvements */
.imageTextBlock img.lazy,
.imageQuoteBlock img.lazy,
.bigHeaderBlock img.lazy {
  opacity: 0;
  transform: scale(1.02);
  transition: opacity 0.4s ease-in-out, transform 0.4s ease-in-out;
  border: none;
  outline: none;
}

.imageTextBlock img.lazy.loaded,
.imageQuoteBlock img.lazy.loaded,
.bigHeaderBlock img.lazy.loaded {
  opacity: 1;
  transform: scale(1);
}

/* Slider images */
.artistSlider img,
.imagesSliderBlock img.lazy {
  border: none;
  outline: none;
  box-shadow: none;
}

.imagesSliderBlock img.lazy {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.imagesSliderBlock img.lazy.loaded {
  opacity: 1;
}

/* Footer images */
footer img.lazy {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

footer img.lazy.loaded {
  opacity: 1;
}

/* Loading placeholder for images without dimensions */
img.lazy:not([width]):not([height]) {
  min-height: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Remove loading animation when loaded */
img.lazy.loaded:not([width]):not([height]) {
  min-height: auto;
  background: none;
  animation: none;
}

/* Ensure no border artifacts on any lazy images */
img.lazy,
img.lazy.loaded {
  border: 0 !important;
  outline: 0 !important;
  box-shadow: none !important;
  /* Prevent any browser default styling */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Smooth transitions for all image containers */
.imageWrapper,
.innerImage,
.backgroundImage {
  overflow: hidden;
}

.imageWrapper img,
.innerImage img,
.backgroundImage img {
  transition: transform 0.3s ease-in-out;
}

/* Responsive improvements */
@media all and (max-width: 1080px) {
  .bottomImage.lazy {
    transform: translateY(15px);
  }
  
  img.lazy:not([width]):not([height]) {
    min-height: 150px;
  }
}

@media all and (max-width: 580px) {
  .bottomImage.lazy {
    transform: translateY(10px);
  }
  
  img.lazy:not([width]):not([height]) {
    min-height: 100px;
  }
  
  /* Faster transitions on mobile for better performance */
  img.lazy {
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  }
}
