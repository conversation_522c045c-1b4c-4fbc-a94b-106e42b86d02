/* Lazy Loading Improvements - Prevent border/flicker */

/* Base lazy loading styles */
img.lazy {
  opacity: 0;
  transition: opacity 0.4s ease-in-out, transform 0.4s ease-in-out;
  background-color: transparent;
  border: none;
  outline: none;
  /* Prevent any default browser styling that could cause borders */
  box-shadow: none;
  /* Ensure smooth rendering */
  image-rendering: auto;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* Loaded state */
img.lazy.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Specific improvements for bottom image */
.bottomImage.lazy {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
  /* Prevent any border artifacts */
  border: none;
  outline: none;
  box-shadow: none;
}

.bottomImage.lazy.loaded {
  opacity: 1;
  transform: translateY(0);
}
