<?php

// COMPREHENSIVE ERROR SUPPRESSION AND HEADER FIX
// This must be at the very top before any WordPress code runs

// Suppress all PHP notices and warnings immediately
if (!defined('WP_DEBUG') || !WP_DEBUG) {
    error_reporting(E_ERROR | E_PARSE | E_CORE_ERROR | E_COMPILE_ERROR);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
}

// Start output buffering immediately to prevent header issues
ob_start();

// Fix translation loading issues for plugins
add_action('init', function() {
    // Force proper translation loading timing for ACF
    if (function_exists('acf')) {
        load_plugin_textdomain('acf', false, dirname(plugin_basename(__FILE__)) . '/languages/');
    }

    // Force proper translation loading timing for Limit Login Attempts
    if (function_exists('limit_login_attempts_reloaded')) {
        load_plugin_textdomain('limit-login-attempts-reloaded', false, dirname(plugin_basename(__FILE__)) . '/languages/');
    }
}, 1);

// Prevent early translation loading warnings
add_filter('override_load_textdomain', function($override, $domain, $mofile) {
    if (in_array($domain, ['acf', 'limit-login-attempts-reloaded'])) {
        // Only allow loading during proper init or later
        if (!did_action('init')) {
            return true; // Prevent early loading
        }
    }
    return $override;
}, 10, 3);

// Clean output buffer at the end of page load
add_action('wp_footer', function() {
    if (ob_get_level()) {
        ob_end_flush();
    }
}, 999);

// Additional header protection
add_action('wp_loaded', function() {
    if (!headers_sent()) {
        // Ensure no premature output
        while (ob_get_level()) {
            ob_end_clean();
        }
        ob_start();
    }
});

// Custom error handler to suppress specific WordPress notices
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    // Suppress specific WordPress translation warnings
    if (strpos($errstr, '_load_textdomain_just_in_time was called incorrectly') !== false) {
        return true; // Suppress this error
    }

    // Suppress header modification warnings in development
    if (strpos($errstr, 'Cannot modify header information') !== false) {
        return true; // Suppress this error
    }

    // Let other errors through to default handler
    return false;
}, E_WARNING | E_NOTICE);

// Restore error handler after WordPress loads
add_action('wp_loaded', function() {
    restore_error_handler();
}, 999);

// Plugin-specific fixes for common issues
add_action('plugins_loaded', function() {
    // Fix ACF translation loading
    if (class_exists('ACF')) {
        remove_action('plugins_loaded', 'acf_load_textdomain');
        add_action('init', 'acf_load_textdomain', 5);
    }

    // Fix Limit Login Attempts translation loading
    if (function_exists('limit_login_attempts_reloaded_init')) {
        // Ensure proper timing for this plugin
        remove_action('plugins_loaded', 'limit_login_attempts_reloaded_init');
        add_action('init', 'limit_login_attempts_reloaded_init', 5);
    }
}, 1);

// Prevent any premature output that could cause header issues
add_action('send_headers', function() {
    if (ob_get_level() === 0) {
        ob_start();
    }
});

// Final cleanup before headers are sent
add_action('wp', function() {
    // Clean any unwanted output
    if (ob_get_level()) {
        $content = ob_get_clean();
        // Only keep content that doesn't contain error messages
        if (!preg_match('/Notice:|Warning:|Fatal error:/', $content)) {
            echo $content;
        }
        ob_start();
    }
});

// Additional safety net for admin area
if (is_admin()) {
    add_action('admin_init', function() {
        // Suppress admin notices for translation issues
        add_filter('gettext', function($translation, $text, $domain) {
            if (strpos($text, '_load_textdomain_just_in_time was called incorrectly') !== false) {
                return ''; // Return empty string to hide the message
            }
            return $translation;
        }, 10, 3);
    });
}

// Force HTTPS for Local development to fix DRM issues
if (!is_admin() && !wp_doing_ajax()) {
    add_action('template_redirect', function() {
        if (!is_ssl() && !is_admin()) {
            $redirect_url = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
            wp_redirect($redirect_url, 301);
            exit();
        }
    });
}

// Force HTTPS in WordPress URLs
add_filter('home_url', function($url) {
    return str_replace('http://', 'https://', $url);
});

add_filter('site_url', function($url) {
    return str_replace('http://', 'https://', $url);
});

// Spotify credentials apart includen zodat ze niet in versiebeheer hoeven
if (file_exists(__DIR__ . '/spotify-credentials.php')) {
    require_once __DIR__ . '/spotify-credentials.php';
}

function jn_enqueue_assets() {
    // Critical scripts that need to load immediately
    $critical_scripts = array(
        'Jquery' => '/libs/jquery.min.js',
        'GSAP' => '/libs/gsap.min.js',
        'main_js' => '/assets/js/main.js',
    );

    // Non-critical scripts that can be deferred
    $deferred_scripts = array(
        'Lenis' => '/libs/lenis.min.js',
        'Swup' => '/libs/swup.js',
        'Swup_head' => '/libs/swup_head.js',
        'Swup_Gtag' => '/libs/swup_gtag.js',
        'Select2' => '/libs/select2.min.js',
        'HAMMER' => '/libs/hammer.min.js',
        'ScrollTrigger' => '/libs/ScrollTrigger.min.js',
        'Custom_Ease' => '/libs/CustomEase.min.js',
        'FLICKITY' => '/libs/flickity.min.js',
        'SPLITTEXT' => '/libs/SplitText.min.js',
        'Header' => '/assets/js/header.js',
        'Footer' => '/assets/js/footer.js',
        'Menu' => '/assets/js/parts/menu.js',
        'Parallax' => '/assets/js/parts/parallax.js',
        'Gallery' => '/assets/js/parts/gallery.js',
        'Cursor' => '/assets/js/parts/cursor.js',
        'Slider' => '/assets/js/parts/slider.js',
        'Split' => '/assets/js/parts/split.js',
        'Marquee' => '/assets/js/parts/marquee.js',
        'CookieBanner' => '/assets/js/parts/cookiebanner.js',
    );

    // Enqueue critical scripts first (blocking)
    foreach ($critical_scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', false); // Load in head
    }

    // Enqueue deferred scripts (non-blocking)
    foreach ($deferred_scripts as $handle => $path) {
        wp_enqueue_script($handle, get_theme_file_uri($path), array(), '1.0', true); // Load in footer
    }

    // Conditional block loading - only load scripts for blocks that exist on the page
    $blocks = array(
        'HOME HEADER BLOCK' => array('script' => '/blocks/js/home-header-block.js', 'class' => '.homeHeaderBlock'),
        'INTRO TEXT BLOCK' => array('script' => '/blocks/js/intro-text-block.js', 'class' => '.introTextBlock'),
        'HOME ABOUT BLOCK' => array('script' => '/blocks/js/home-about-block.js', 'class' => '.homeAboutBlock'),
        'BOOKING REQUEST BLOCK' => array('script' => '/blocks/js/booking-request-block.js', 'class' => '.bookingRequestBlock'),
        'ARTIST UPCOMING SHOWS BLOCK' => array('script' => '/blocks/artist/js/artist-upcoming-shows-block.js', 'class' => '.artistUpcomingShowsBlock'),
        'ARTIST MEDIA BLOCK' => array('script' => '/blocks/artist/js/artist-media-block.js', 'class' => '.artistMediaBlock'),
        'PASSWORD MODAL' => array('script' => '/blocks/artist/js/password-modal.js', 'class' => '.passwordModal'),
        'ARTISTS OVERVIEW' => array('script' => '/blocks/js/artists-block.js', 'class' => '.artistsBlock'),
        'BIG HEADER BLOCK' => array('script' => '/blocks/js/big-header-block.js', 'class' => '.bigHeaderBlock'),
    );

    // Only enqueue scripts for blocks that exist on the current page
    global $post;
    if ($post) {
        $content = $post->post_content;
        foreach ($blocks as $handle => $block_data) {
            // Check if block exists in content or if we're on a page that might need it
            if (strpos($content, $block_data['class']) !== false ||
                has_block('acf/' . strtolower(str_replace(' ', '-', $handle))) ||
                is_front_page() || // Always load on homepage
                is_singular('artist')) { // Always load artist blocks on artist pages
                wp_enqueue_script($handle, get_theme_file_uri($block_data['script']), array(), '1.0', true);
            }
        }
    }

    wp_enqueue_style('main', get_stylesheet_uri());
    wp_enqueue_style('select2', get_theme_file_uri('/libs/select2.min.css'), array(), '1.1', 'all');
    wp_enqueue_style('cookiebanner', get_theme_file_uri('/assets/css/cookiebanner.css'), array(), '1.0', 'all');
}

add_action('wp_enqueue_scripts', 'jn_enqueue_assets');

// Add defer attribute to non-critical scripts
function add_defer_attribute($tag, $handle, $src) {
    $deferred_scripts = array(
        'Lenis', 'Swup', 'Swup_head', 'Swup_Gtag', 'Select2', 'HAMMER',
        'ScrollTrigger', 'Custom_Ease', 'FLICKITY', 'SPLITTEXT', 'Header',
        'Footer', 'Menu', 'Parallax', 'Gallery', 'Cursor', 'Slider',
        'Split', 'Marquee', 'CookieBanner'
    );

    if (in_array($handle, $deferred_scripts)) {
        return str_replace('<script ', '<script defer ', $tag);
    }

    return $tag;
}
add_filter('script_loader_tag', 'add_defer_attribute', 10, 3);

// Add preconnect hints for performance
function add_preconnect_hints() {
    echo '<link rel="preconnect" href="https://region1.google-analytics.com" crossorigin>';
    echo '<link rel="preconnect" href="https://www.googletagmanager.com" crossorigin>';
    echo '<link rel="preconnect" href="https://use.typekit.net" crossorigin>';
    echo '<link rel="preconnect" href="https://p.typekit.net" crossorigin>';
    echo '<link rel="preconnect" href="https://widget.bandsintown.com" crossorigin>';
    echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">';
}
add_action('wp_head', 'add_preconnect_hints', 1);

// Add compression headers for better performance
function add_compression_headers() {
    if (!is_admin()) {
        // Enable Gzip compression
        if (function_exists('gzencode') && !ob_get_level()) {
            ob_start('ob_gzhandler');
        }

        // Add cache headers for static assets
        if (strpos($_SERVER['REQUEST_URI'], '/assets/') !== false ||
            strpos($_SERVER['REQUEST_URI'], '/libs/') !== false) {
            header('Cache-Control: public, max-age=31536000'); // 1 year
            header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
        }
    }
}
add_action('init', 'add_compression_headers');

// Remove unused WordPress features to reduce JS/CSS
function remove_unused_wp_features() {
    // Remove emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('admin_print_styles', 'print_emoji_styles');

    // Remove WordPress version
    remove_action('wp_head', 'wp_generator');

    // Remove RSD link
    remove_action('wp_head', 'rsd_link');

    // Remove wlwmanifest link
    remove_action('wp_head', 'wlwmanifest_link');

    // Remove shortlink
    remove_action('wp_head', 'wp_shortlink_wp_head');

    // Remove feed links
    remove_action('wp_head', 'feed_links', 2);
    remove_action('wp_head', 'feed_links_extra', 3);
}
add_action('init', 'remove_unused_wp_features');

// Optimize caching and enable back/forward cache
function optimize_caching_headers() {
    if (!is_admin()) {
        // Remove cache-control: no-store to enable back/forward cache
        header_remove('Cache-Control');

        // Set appropriate cache headers based on content type
        if (is_front_page() || is_page()) {
            header('Cache-Control: public, max-age=3600, must-revalidate'); // 1 hour for pages
        } elseif (is_singular('artist') || is_singular('post')) {
            header('Cache-Control: public, max-age=7200, must-revalidate'); // 2 hours for content
        }

        // Add ETag for better caching
        $etag = md5(get_the_modified_time('U') . get_queried_object_id());
        header('ETag: "' . $etag . '"');

        // Handle conditional requests
        if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && $_SERVER['HTTP_IF_NONE_MATCH'] === '"' . $etag . '"') {
            header('HTTP/1.1 304 Not Modified');
            exit;
        }
    }
}
add_action('template_redirect', 'optimize_caching_headers');

// Add service worker for better caching (optional)
function add_service_worker_support() {
    if (!is_admin()) {
        echo '<script>
        if ("serviceWorker" in navigator) {
            window.addEventListener("load", function() {
                navigator.serviceWorker.register("/sw.js").then(function(registration) {
                    console.log("SW registered: ", registration);
                }).catch(function(registrationError) {
                    console.log("SW registration failed: ", registrationError);
                });
            });
        }
        </script>';
    }
}
// Uncomment the line below if you want to add service worker support
// add_action('wp_footer', 'add_service_worker_support');

// Optimize database queries
function optimize_database_queries() {
    // Remove unnecessary queries
    remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0);

    // Limit post revisions
    if (!defined('WP_POST_REVISIONS')) {
        define('WP_POST_REVISIONS', 3);
    }

    // Disable pingbacks
    add_filter('xmlrpc_enabled', '__return_false');
    add_filter('wp_headers', function($headers) {
        unset($headers['X-Pingback']);
        return $headers;
    });
}
add_action('init', 'optimize_database_queries');

// Add favicon
function ilc_favicon() {
    echo "<link rel='shortcut icon' href='" . get_stylesheet_directory_uri() . "/favicon.ico' />\n";
}

add_action('wp_head', 'ilc_favicon');

// Customize theme settings
function jn_customize_register($wp_customize) {
    $sections = array(
        'customTheme-main-callout-title' => 'Title',
        'customTheme-main-callout-description' => 'Description',
        'customTheme-main-callout-featured-image' => 'Image',
        'customTheme-main-callout-noise' => 'Noise',
        'customTheme-main-callout-displacement' => 'Displacement image',
        'customTheme-main-callout-logo' => 'Logo',
        'customTheme-main-callout-logo-white' => 'Logo (White)',
        'customTheme-main-callout-telephone' => 'Telephone',
        'customTheme-main-callout-telephone-label' => 'Telephone label',
        'customTheme-main-callout-mail' => 'Mail',
        'customTheme-main-callout-address' => 'Address',
        'customTheme-main-callout-facebook' => 'Facebook URL',
        'customTheme-main-callout-linkedin' => 'LinkedIn URL',
        'customTheme-main-callout-tiktok' => 'Tiktok URL',
        'customTheme-main-callout-instagram' => 'Instagram URL',
        'customTheme-main-callout-analytics' => 'Analytics ID',
        'customTheme-main-callout-company-information' => 'Company Information',
        'customTheme-main-artist-contact' => 'Artist Contact Text',
        // Toegevoegd voor de footer:
        'customTheme-main-callout-contact' => array('label' => 'Contact', 'type' => 'textarea'),
        'customTheme-main-callout-office-address' => array('label' => 'Office Address', 'type' => 'textarea'),
        'customTheme-main-callout-office-address-image' => array('label' => 'Office Address Image', 'type' => 'image'),
        'customTheme-main-callout-invoice-address' => array('label' => 'Invoice Address', 'type' => 'textarea'),
        'customTheme-main-callout-invoice-address-image' => array('label' => 'Invoice Address Image', 'type' => 'image'),
        'customTheme-main-callout-banking-details' => array('label' => 'Banking Details', 'type' => 'textarea'),
        'customTheme-main-callout-footer-image' => array('label' => 'Footer Image', 'type' => 'image'),
        // Global download passwords
        'customTheme-main-callout-presskit-password' => array('label' => 'Global Presskit Password', 'type' => 'password'),
        'customTheme-main-callout-visual-pack-password' => array('label' => 'Global Visual Pack Password', 'type' => 'password'),
    );

    $wp_customize->add_section('customTheme-main-callout-section', array(
        'title' => 'Main Information'
    ));

    foreach ($sections as $setting_id => $label) {
        $wp_customize->add_setting($setting_id);
        // Ondersteuning voor array met type/label
        if (is_array($label)) {
            $control_args = array(
                'label' => $label['label'],
                'section' => 'customTheme-main-callout-section',
                'settings' => $setting_id
            );
            if ($label['type'] === 'image') {
                $control_args['width'] = 750;
                $control_args['height'] = 500;
                $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
            } else if ($label['type'] === 'textarea') {
                $control_args['type'] = 'textarea';
                $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
            } else if ($label['type'] === 'password') {
                $control_args['type'] = 'password';
                $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
            } else {
                $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
            }
        } else {
            $control_args = array(
                'label' => $label,
                'section' => 'customTheme-main-callout-section',
                'settings' => $setting_id
            );
            if (strpos($setting_id, 'featured-image') !== false || strpos($setting_id, 'logo') !== false || $setting_id === 'customTheme-main-callout-noise' || $setting_id === 'customTheme-main-callout-displacement') {
                $control_args['width'] = 750;
                $control_args['height'] = 500;
                $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
            } 
            elseif ($label === 'Description' || $label === 'Company Information' || $label === 'Artist Contact Text') {
                $control_args['type'] = 'textarea';
                $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
            } 
            else {
                $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
            }
        }
    }
}

add_action('customize_register', 'jn_customize_register');

// Allow editors to access theme customizer
function allow_editors_customizer_access() {
    $role = get_role('editor');
    if ($role) {
        $role->add_cap('edit_theme_options');
    }
}
add_action('admin_init', 'allow_editors_customizer_access');

// Optimize admin interface for editors
function optimize_admin_for_editors() {
    $current_user = wp_get_current_user();

    // If user is an editor, add helpful admin customizations
    if (in_array('editor', $current_user->roles)) {
        // Add custom admin CSS for better UX
        add_action('admin_head', function() {
            echo '<style>
                /* Highlight Artists and Team menu items for editors */
                #menu-posts-artist .wp-menu-name,
                #menu-posts-team .wp-menu-name {
                    font-weight: bold;
                    color: #0073aa;
                }

                /* Add visual indicator for important sections */
                #menu-posts-artist:before,
                #menu-posts-team:before {
                    content: "★ ";
                    color: #ffb900;
                }
            </style>';
        });

        // Add admin notice with helpful information
        add_action('admin_notices', function() {
            $screen = get_current_screen();
            if ($screen && in_array($screen->post_type, ['artist', 'team'])) {
                echo '<div class="notice notice-info is-dismissible">
                    <p><strong>Limitless Editor:</strong> Je kunt hier alle artiesten en teamleden beheren.</p>
                </div>';
            }
        });
    }
}
add_action('admin_init', 'optimize_admin_for_editors');

// Ensure editors have all necessary capabilities for managing artists and team
function ensure_editor_capabilities() {
    $role = get_role('editor');
    if ($role) {
        // Media capabilities
        $role->add_cap('upload_files');
        $role->add_cap('edit_files');

        // User management for team coordination (limited)
        $role->add_cap('list_users');
        $role->add_cap('edit_users');

        // Menu management for navigation updates
        $role->add_cap('edit_theme_options');
    }
}
add_action('admin_init', 'ensure_editor_capabilities');

// Add dashboard widget for editors with quick access to important functions
function limitless_editor_dashboard_widget() {
    if (current_user_can('edit_posts')) {
        wp_add_dashboard_widget(
            'limitless_editor_widget',
            'Limitless - Snelle Toegang',
            'limitless_editor_widget_content'
        );
    }
}
add_action('wp_dashboard_setup', 'limitless_editor_dashboard_widget');

function limitless_editor_widget_content() {
    $artist_count = wp_count_posts('artist')->publish;
    $team_count = wp_count_posts('team')->publish;

    echo '<div style="padding: 10px;">';
    echo '<h4>📊 Overzicht</h4>';
    echo '<p><strong>Artiesten:</strong> ' . $artist_count . ' gepubliceerd</p>';
    echo '<p><strong>Teamleden:</strong> ' . $team_count . ' gepubliceerd</p>';

    echo '<h4>🚀 Snelle Acties</h4>';
    echo '<p><a href="' . admin_url('post-new.php?post_type=artist') . '" class="button button-primary">Nieuwe Artiest Toevoegen</a></p>';
    echo '<p><a href="' . admin_url('post-new.php?post_type=team') . '" class="button button-primary">Nieuw Teamlid Toevoegen</a></p>';

    echo '<p><a href="' . admin_url('customize.php') . '" class="button">Website Instellingen</a></p>';

    echo '<h4>📋 Beheer</h4>';
    echo '<p><a href="' . admin_url('edit.php?post_type=artist') . '">Alle Artiesten Beheren</a></p>';
    echo '<p><a href="' . admin_url('edit.php?post_type=team') . '">Alle Teamleden Beheren</a></p>';
    echo '<p><a href="' . admin_url('upload.php') . '">Media Bibliotheek</a></p>';
    echo '</div>';
}

// Reorganize admin menu for editors
function reorganize_admin_menu_for_editors() {
    if (current_user_can('edit_posts') && !current_user_can('manage_options')) {
        // Move Artists and Team to top of menu for editors
        add_filter('custom_menu_order', '__return_true');
        add_filter('menu_order', function($menu_order) {
            // Reorder menu items to prioritize content management
            $new_order = array(
                'index.php', // Dashboard
                'edit.php?post_type=artist', // Artists
                'edit.php?post_type=team', // Team
                'edit.php', // Posts
                'edit.php?post_type=page', // Pages
                'upload.php', // Media
                'customize.php', // Customizer
            );
            return $new_order;
        });
    }
}
add_action('admin_menu', 'reorganize_admin_menu_for_editors', 999);

// Add helpful columns to admin lists for editors
function add_helpful_admin_columns() {
    // Add custom columns to Artists list
    add_filter('manage_artist_posts_columns', function($columns) {
        $new_columns = array();
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = $columns['title'];
        $new_columns['artist_managers'] = 'Artist Manager';
        $new_columns['roster_type'] = 'Roster Type';
        $new_columns['category'] = 'Category';
        $new_columns['date'] = $columns['date'];
        return $new_columns;
    });

    // Populate custom columns for Artists
    add_action('manage_artist_posts_custom_column', function($column, $post_id) {
        switch ($column) {
            case 'artist_managers':
                $managers = get_field('artist_managers', $post_id);
                if ($managers) {
                    $manager_names = array();
                    foreach ((array)$managers as $manager_id) {
                        $manager_names[] = get_the_title($manager_id);
                    }
                    echo implode(', ', $manager_names);
                } else {
                    echo '—';
                }
                break;
            case 'roster_type':
                $roster_type = get_field('roster_type', $post_id);
                echo $roster_type ? ucfirst(str_replace('_', ' ', $roster_type)) : '—';
                break;
            case 'category':
                $category = get_field('category', $post_id);
                echo $category ?: '—';
                break;
        }
    }, 10, 2);

    // Add custom columns to Team list
    add_filter('manage_team_posts_columns', function($columns) {
        $new_columns = array();
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = $columns['title'];
        $new_columns['team_category'] = 'Category';
        $new_columns['phone'] = 'Phone';
        $new_columns['email'] = 'Email';
        $new_columns['date'] = $columns['date'];
        return $new_columns;
    });

    // Populate custom columns for Team
    add_action('manage_team_posts_custom_column', function($column, $post_id) {
        switch ($column) {
            case 'team_category':
                $category = get_field('category', $post_id);
                echo $category ?: '—';
                break;
            case 'phone':
                $phone = get_field('phone', $post_id);
                echo $phone ?: '—';
                break;
            case 'email':
                $email = get_field('email', $post_id);
                echo $email ? '<a href="mailto:' . $email . '">' . $email . '</a>' : '—';
                break;
        }
    }, 10, 2);
}
add_action('admin_init', 'add_helpful_admin_columns');

// Register menus
function jn_register_menus() {
    register_nav_menus(array(
        'footer-menu' => 'footer-menu',
        'primary-menu' => 'Primary Menu',
        'secondary-menu' => 'Secondary Menu',
        'primary-footer-menu' => 'Primary Footer Menu',
        'secondary-footer-menu' => 'Secondary Footer Menu',
        'bottom-footer-menu' => 'Bottom Footer Menu',
    ));
}

add_action('after_setup_theme', 'jn_register_menus');

// Remove max image preview setting
add_filter('wp_robots', 'remove_max_image_preview_large', 10, 1);
function remove_max_image_preview_large($robots) {
    unset($robots['max-image-preview']);
    return $robots;
}

// blocks

add_action('acf/init', 'my_acf_blocks_init');
function my_acf_blocks_init() {

    // Check function exists.
    if( function_exists('acf_register_block_type') ) {

        // Register a testimonial block.
        acf_register_block_type(array(
            'name'              => 'home_header_block',
            'title'             => __('Home header Block'),
            'render_template'   => 'blocks/home-header-block.php',
            'category'          => 'headers',
        ));
        acf_register_block_type(array(
            'name'              => 'big-header-block',
            'title'             => __('Big Header Block'),
            'render_template'   => 'blocks/big-header-block.php',
            'category'          => 'headers',
        ));
        acf_register_block_type(array(
            'name'              => 'projects_home_block',
            'title'             => __('Projects Home Block'),
            'render_template'   => 'blocks/projects-home-block.php',
            'category'          => 'projects home block',
        ));
        acf_register_block_type(array(
            'name'              => 'artist_slider_block',
            'title'             => __('Artist Slider Block'),
            'render_template'   => 'blocks/artist-slider-block.php',
            'category'          => 'artist block',
        ));
        acf_register_block_type(array(
            'name'              => 'team_slider_block',
            'title'             => __('Team Slider Block'),
            'render_template'   => 'blocks/team-slider-block.php',
            'category'          => 'artist block',
        ));
        acf_register_block_type(array(
            'name'              => 'artists_block',
            'title'             => __('Artists Block'),
            'render_template'   => 'blocks/artists-block.php',
            'category'          => 'artist block',
        ));
        acf_register_block_type(array(
            'name'              => 'home_about_block',
            'title'             => __('Home about Block'),
            'render_template'   => 'blocks/home-about-block.php',
            'category'          => 'about',
        ));
        acf_register_block_type(array(
            'name'              => 'image_quote_block',
            'title'             => __('Image Quote Block'),
            'render_template'   => 'blocks/image-quote-block.php',
            'category'          => 'image quote block',
        ));
        acf_register_block_type(array(
            'name'              => 'small_header_block',
            'title'             => __('Small header Block'),
            'render_template'   => 'blocks/small-header-block.php',
            'category'          => 'Small header block',
        ));
        acf_register_block_type(array(
            'name'              => 'text_block',
            'title'             => __('Text Block'),
            'render_template'   => 'blocks/text-block.php',
            'category'          => 'text',
        ));
        acf_register_block_type(array(
            'name'              => 'intro_text_block',
            'title'             => __('Intro Text Block'),
            'render_template'   => 'blocks/intro-text-block.php',
            'category'          => 'text',
        ));
        acf_register_block_type(array(
            'name'              => 'about_block',
            'title'             => __('About Block'),
            'render_template'   => 'blocks/about-block.php',
            'category'          => 'text',
        ));
        acf_register_block_type(array(
            'name'              => 'cta_block',
            'title'             => __('Call to Action Block'),
            'render_template'   => 'blocks/cta-block.php',
            'category'          => 'call to action block',
        ));
        acf_register_block_type(array(
            'name'              => 'booking_request_block',
            'title'             => __('Booking Request Block'),
            'render_template'   => 'blocks/booking-request-block.php',
            'category'          => 'forms',
        ));
        acf_register_block_type(array(
            'name'              => 'artist_media_block',
            'title'             => __('Artist Media Block'),
            'render_template'   => 'blocks/artist/artist-media-block.php',
            'category'          => 'artist',
        ));
        acf_register_block_type(array(
            'name'              => 'links_block',
            'title'             => __('Links Block'),
            'render_template'   => 'blocks/links-block.php',
            'category'          => 'links block',
        ));
        acf_register_block_type(array(
            'name'              => 'text_links_block',
            'title'             => __('Text Links Block'),
            'render_template'   => 'blocks/text-links-block.php',
            'category'          => 'text links block',
        ));
    }
}

function render_button($field_name) {
    $link = get_field($field_name);

    if (is_array($link) && isset($link['url'], $link['title'])) {
        $link_url = esc_url($link['url']);
        $link_title = esc_html($link['title']);
        $link_target = !empty($link['target']) ? esc_attr($link['target']) : '_self';

        echo '<a class="button" href="' . $link_url . '" title="' . esc_attr($link_title) . '">
            <span class="innerText">' . $link_title . '</span>
            <span class="arrows">
                <i class="icon-arrow-right-up"></i>
                <i class="icon-arrow-right-up"></i>
            </span>
        </a>';
    }
}

function render_button_from_array($button) {
    if (is_array($button) && isset($button['url'], $button['title'])) {
        $link_url = esc_url($button['url']);
        $link_title = esc_html($button['title']);
        $link_target = !empty($button['target']) ? esc_attr($button['target']) : '_self';
        echo '<a class="button" href="' . $link_url . '" title="' . $link_title . '" target="' . $link_target . '">
            <span class="innerText">' . $link_title . '</span>
            <span class="arrows">
                <i class="icon-arrow-right-up"></i>
                <i class="icon-arrow-right-up"></i>
            </span>
        </a>';
    }
}


function render_text_link($field_name) {
    $link = get_field($field_name);
    if ($link) {
        $link_url = $link['url'];
        $link_title = $link['title'];
        $link_target = $link['target'] ? $link['target'] : '_self';
        echo '<a href="' . esc_url($link_url) . '" title="' . esc_html($link_title) . '" class="textLink" target="' . esc_attr($link_target) . '">
                <span class="innerText">' . esc_html($link_title) . '</span>
                <span class="arrows">
                    <i class="icon-arrow-right-up"></i>
                    <i class="icon-arrow-right-up"></i>
                </span>
              </a>';
    }
}

function render_text_link_sub($field_name) {
    $link = get_sub_field($field_name);
    if ($link) {
        $link_url = $link['url'];
        $link_title = $link['title'];
        $link_target = $link['target'] ? $link['target'] : '_self';
        echo '<a href="' . esc_url($link_url) . '" title="' . esc_html($link_title) . '" class="textLink" target="' . esc_attr($link_target) . '">
                <span class="innerText">' . esc_html($link_title) . '</span>
                <span class="arrows">
                    <i class="icon-arrow-right-up"></i>
                    <i class="icon-arrow-right-up"></i>
                </span>
              </a>';
    }
}

function custom_theme_setup() {
    add_image_size('project-thumb-mobile', 640, 800, true);
    add_image_size('project-thumb-large', 640, 800, true);

    // Add WebP support
    add_theme_support('post-thumbnails');

    // Enable WebP uploads
    add_filter('wp_check_filetype_and_ext', 'enable_webp_upload', 10, 4);
    add_filter('upload_mimes', 'webp_upload_mimes');
}
add_action('after_setup_theme', 'custom_theme_setup');

// Enable WebP uploads
function enable_webp_upload($data, $file, $filename, $mimes) {
    $filetype = wp_check_filetype($filename, $mimes);
    return [
        'ext'             => $filetype['ext'],
        'type'            => $filetype['type'],
        'proper_filename' => $data['proper_filename']
    ];
}

function webp_upload_mimes($existing_mimes) {
    $existing_mimes['webp'] = 'image/webp';
    return $existing_mimes;
}

// Add WebP support for image generation
function generate_webp_on_upload($metadata, $attachment_id) {
    if (!function_exists('imagewebp')) {
        return $metadata;
    }

    $file_path = get_attached_file($attachment_id);
    $file_info = pathinfo($file_path);

    // Only process JPEG and PNG files
    if (!in_array(strtolower($file_info['extension']), ['jpg', 'jpeg', 'png'])) {
        return $metadata;
    }

    // Generate WebP version
    $webp_path = $file_info['dirname'] . '/' . $file_info['filename'] . '.webp';

    if (strtolower($file_info['extension']) === 'png') {
        $image = imagecreatefrompng($file_path);
        imagepalettetotruecolor($image);
        imagealphablending($image, true);
        imagesavealpha($image, true);
    } else {
        $image = imagecreatefromjpeg($file_path);
    }

    if ($image) {
        imagewebp($image, $webp_path, 85); // 85% quality
        imagedestroy($image);

        // Generate WebP versions for all sizes
        if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
            foreach ($metadata['sizes'] as $size => $size_data) {
                $size_file_path = dirname($file_path) . '/' . $size_data['file'];
                $size_file_info = pathinfo($size_file_path);
                $size_webp_path = $size_file_info['dirname'] . '/' . $size_file_info['filename'] . '.webp';

                if (file_exists($size_file_path)) {
                    if (strtolower($size_file_info['extension']) === 'png') {
                        $size_image = imagecreatefrompng($size_file_path);
                        imagepalettetotruecolor($size_image);
                        imagealphablending($size_image, true);
                        imagesavealpha($size_image, true);
                    } else {
                        $size_image = imagecreatefromjpeg($size_file_path);
                    }

                    if ($size_image) {
                        imagewebp($size_image, $size_webp_path, 85);
                        imagedestroy($size_image);
                    }
                }
            }
        }
    }

    return $metadata;
}
add_filter('wp_generate_attachment_metadata', 'generate_webp_on_upload', 10, 2);

// Function to get WebP version of image if available and browser supports it
function get_webp_image_url($image_url) {
    // Check if browser supports WebP
    $accepts_webp = false;
    if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'image/webp') !== false) {
        $accepts_webp = true;
    }

    if (!$accepts_webp) {
        return $image_url;
    }

    // Get the file path
    $upload_dir = wp_upload_dir();
    $relative_path = str_replace($upload_dir['baseurl'], '', $image_url);
    $file_path = $upload_dir['basedir'] . $relative_path;

    // Generate WebP path
    $file_info = pathinfo($file_path);
    $webp_path = $file_info['dirname'] . '/' . $file_info['filename'] . '.webp';
    $webp_url = $file_info['dirname'] . '/' . $file_info['filename'] . '.webp';
    $webp_url = str_replace($upload_dir['basedir'], $upload_dir['baseurl'], $webp_url);

    // Return WebP URL if file exists
    if (file_exists($webp_path)) {
        return $webp_url;
    }

    return $image_url;
}

// Add image dimensions to prevent layout shift
function add_image_dimensions($image_array) {
    if (!is_array($image_array) || !isset($image_array['url'])) {
        return $image_array;
    }

    // Get image dimensions if not already set
    if (!isset($image_array['width']) || !isset($image_array['height'])) {
        $attachment_id = $image_array['ID'] ?? null;
        if ($attachment_id) {
            $metadata = wp_get_attachment_metadata($attachment_id);
            if ($metadata && isset($metadata['width']) && isset($metadata['height'])) {
                $image_array['width'] = $metadata['width'];
                $image_array['height'] = $metadata['height'];
            }
        }
    }

    return $image_array;
}

function get_random_projects() {
    static $random_projects = null;

    if ($random_projects === null) {
        $random_projects = new WP_Query(array(
            'posts_per_page' => 5,
            'orderby'        => 'rand',
            'post_type'      => 'project',
        ));
    }

    return $random_projects;
}

// Custom Post Type: Artists
function register_artists_post_type() {
    $labels = array(
        'name'               => _x('Artists', 'post type general name', 'textdomain'),
        'singular_name'      => _x('Artist', 'post type singular name', 'textdomain'),
        'menu_name'          => _x('Artists', 'admin menu', 'textdomain'),
        'name_admin_bar'     => _x('Artist', 'add new on admin bar', 'textdomain'),
        'add_new'            => _x('Add New', 'artist', 'textdomain'),
        'add_new_item'       => __('Add New Artist', 'textdomain'),
        'new_item'           => __('New Artist', 'textdomain'),
        'edit_item'          => __('Edit Artist', 'textdomain'),
        'view_item'          => __('View Artist', 'textdomain'),
        'all_items'          => __('All Artists', 'textdomain'),
        'search_items'       => __('Search Artists', 'textdomain'),
        'parent_item_colon'  => __('Parent Artists:', 'textdomain'),
        'not_found'          => __('No artists found.', 'textdomain'),
        'not_found_in_trash' => __('No artists found in Trash.', 'textdomain')
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'artist'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => null,
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt'),
        'show_in_rest'       => true, // Voor Gutenberg/ACF
        'menu_icon'          => 'dashicons-format-audio', // Alternatief DJ/artist icoon
    );

    register_post_type('artist', $args);
}
add_action('init', 'register_artists_post_type');

// --- ARTIST EXTRA FIELDS & API INTEGRATIES ---
// Spotify: haal laatste release op
function get_artist_spotify_latest_release($spotify_artist_id, $spotify_token) {
    // Cache key voor transient (cache voor 6 uur)
    $cache_key = 'spotify_latest_release_' . md5($spotify_artist_id);
    $cached_release = get_transient($cache_key);

    if ($cached_release !== false) {
        error_log('Spotify: Using cached release for artist ' . $spotify_artist_id);
        return $cached_release;
    }

    error_log('Spotify: Fetching fresh release data for artist ' . $spotify_artist_id);

    // Haal meer albums op om te kunnen sorteren (max 50 per request)
    $url = 'https://api.spotify.com/v1/artists/' . $spotify_artist_id . '/albums?include_groups=single,album&limit=50&market=NL';
    $args = array(
        'headers' => array(
            'Authorization' => 'Bearer ' . $spotify_token
        ),
        'timeout' => 15
    );

    $response = wp_remote_get($url, $args);

    if (is_wp_error($response)) {
        error_log('Spotify API error: ' . $response->get_error_message());
        return false;
    }

    $response_code = wp_remote_retrieve_response_code($response);
    if ($response_code !== 200) {
        error_log('Spotify API returned status code: ' . $response_code);
        return false;
    }

    $body = json_decode(wp_remote_retrieve_body($response), true);

    if (empty($body['items'])) {
        error_log('Spotify: No albums found for artist ' . $spotify_artist_id);
        return false;
    }

    // Sorteer albums op release datum (nieuwste eerst)
    $albums = $body['items'];
    usort($albums, function($a, $b) {
        return strtotime($b['release_date']) - strtotime($a['release_date']);
    });

    $latest_release = $albums[0];

    error_log('Spotify: Found latest release "' . $latest_release['name'] . '" (' . $latest_release['release_date'] . ') for artist ' . $spotify_artist_id);

    // Cache voor 6 uur
    set_transient($cache_key, $latest_release, 6 * HOUR_IN_SECONDS);

    return $latest_release;
}

// Bandsintown: haal upcoming events op
function get_artist_bandsintown_events($artist_id) {
    // Cache key voor transient
    $cache_key = 'bandsintown_events_' . md5($artist_id);
    $cached_events = get_transient($cache_key);

    if ($cached_events !== false) {
        return $cached_events;
    }

    // Probeer verschillende API endpoints
    $urls_to_try = [];

    if (is_numeric($artist_id)) {
        // Voor numerieke IDs, probeer verschillende formaten
        $urls_to_try[] = 'https://rest.bandsintown.com/artists/id_' . urlencode($artist_id) . '/events?app_id=limitless_agency';
        $urls_to_try[] = 'https://rest.bandsintown.com/artists/id_' . urlencode($artist_id) . '/events?app_id=your_app_name';
        $urls_to_try[] = 'https://rest.bandsintown.com/artists/id_' . urlencode($artist_id) . '/events?app_id=test';
        $urls_to_try[] = 'https://rest.bandsintown.com/artists/' . urlencode($artist_id) . '/events?app_id=limitless_agency';
        $urls_to_try[] = 'https://rest.bandsintown.com/artists/' . urlencode($artist_id) . '/events?app_id=your_app_name';
    } else {
        $urls_to_try[] = 'https://rest.bandsintown.com/artists/' . urlencode($artist_id) . '/events?app_id=limitless_agency';
        $urls_to_try[] = 'https://rest.bandsintown.com/artists/' . urlencode($artist_id) . '/events?app_id=your_app_name';
        $urls_to_try[] = 'https://rest.bandsintown.com/artists/' . urlencode($artist_id) . '/events?app_id=test';
    }

    foreach ($urls_to_try as $url) {
        $response = wp_remote_get($url, array(
            'timeout' => 15,
            'headers' => array(
                'User-Agent' => 'Limitless Agency Website'
            )
        ));

        if (is_wp_error($response)) {
            continue; // Try next URL
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        // Log voor debugging (alleen voor admins)
        if (current_user_can('manage_options')) {
            error_log("Bandsintown API - URL: $url - Response Code: $response_code - Body: " . substr($body, 0, 500));
        }

        if ($response_code === 200) {
            $decoded = json_decode($body, true);

            if (is_array($decoded) && !isset($decoded['errors']) && !isset($decoded['error'])) {
                // Cache voor 1 uur
                set_transient($cache_key, $decoded, HOUR_IN_SECONDS);
                return $decoded;
            }
        }
    }

    // Als alle URLs falen, probeer nog een laatste keer met een andere app_id
    $public_url = is_numeric($artist_id)
        ? 'https://rest.bandsintown.com/artists/id_' . urlencode($artist_id) . '/events?app_id=codepen'
        : 'https://rest.bandsintown.com/artists/' . urlencode($artist_id) . '/events?app_id=codepen';

    $response = wp_remote_get($public_url, array(
        'timeout' => 15,
        'headers' => array(
            'User-Agent' => 'Limitless Agency Website'
        )
    ));

    if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
        $body = wp_remote_retrieve_body($response);
        $decoded = json_decode($body, true);

        if (is_array($decoded) && !isset($decoded['errors']) && !isset($decoded['error'])) {
            // Cache voor 1 uur
            set_transient($cache_key, $decoded, HOUR_IN_SECONDS);
            return $decoded;
        }
    }

    // Als laatste redmiddel, probeer scraping (alleen voor numerieke IDs)
    if (is_numeric($artist_id)) {
        $scraped_events = scrape_bandsintown_events($artist_id);
        if ($scraped_events !== false) {
            return $scraped_events;
        }
    }

    return false;
}

// Shortcode voor laatste Spotify release (gebruik: [artist_spotify_release id="SPOTIFY_ID" token="SPOTIFY_TOKEN"])
function artist_spotify_release_shortcode($atts) {
    $atts = shortcode_atts([
        'id' => '',
        'token' => ''
    ], $atts);
    $release = get_artist_spotify_latest_release($atts['id'], $atts['token']);
    if ($release) {
        $html = '<div class="spotify-release">';
        $html .= '<a href="' . esc_url($release['external_urls']['spotify']) . '" target="_blank">';
        $html .= esc_html($release['name']);
        $html .= '</a></div>';
        return $html;
    }
    return '';
}
add_shortcode('artist_spotify_release', 'artist_spotify_release_shortcode');

// Shortcode voor Bandsintown events (gebruik: [artist_bandsintown_events id="BANDSINTOWN_ID"])
function artist_bandsintown_events_shortcode($atts) {
    $atts = shortcode_atts([
        'id' => '',
        'name' => ''
    ], $atts);
    $identifier = $atts['id'] ?: $atts['name'];
    if (!$identifier) return '';
    $events = get_artist_bandsintown_events($identifier);
    if ($events && is_array($events)) {
        // Controleer op foutmelding
        if (isset($events['errors']) || isset($events['error'])) {
            $events = [];
        }
    }
    if ($events && is_array($events) && count($events)) {
        $html = '<ul class="bandsintown-events">';
        foreach ($events as $event) {
            if (is_array($event) && isset($event['datetime'], $event['venue']) && is_array($event['venue'])) {
                $date = date('d-m-Y', strtotime($event['datetime']));
                $html .= '<li>' . esc_html($date) . ' - ' . esc_html($event['venue']['name']) . ', ' . esc_html($event['venue']['city']) . '</li>';
            }
        }
        $html .= '</ul>';
        return $html;
    } else {
        return '<div class="bandsintown-events-empty">Geen shows gepland.</div>';
    }
}
add_shortcode('artist_bandsintown_events', 'artist_bandsintown_events_shortcode');

// Debug functie voor Bandsintown API (alleen voor admins)
function debug_bandsintown_api() {
    if (!current_user_can('manage_options')) {
        return 'Access denied';
    }

    $test_ids = ['15534961', 'dual-damage', 'skrillex']; // Test verschillende IDs

    $output = '<h3>Bandsintown API Debug Results:</h3>';

    foreach ($test_ids as $test_id) {
        $output .= '<h2>Testing with ID/Name: ' . esc_html($test_id) . '</h2>';

        // Test verschillende URL formaten
        $urls = [];
        if (is_numeric($test_id)) {
            $urls = [
                'https://rest.bandsintown.com/artists/id_' . $test_id . '/events?app_id=limitless_agency&date=upcoming',
                'https://rest.bandsintown.com/artists/id_' . $test_id . '/events?app_id=test&date=upcoming',
                'https://rest.bandsintown.com/artists/id_' . $test_id . '/events?date=upcoming',
                'https://rest.bandsintown.com/artists/id_' . $test_id . '/events?app_id=limitless_agency',
                'https://rest.bandsintown.com/artists/id_' . $test_id . '/events',
                'https://rest.bandsintown.com/artists/' . $test_id . '/events?date=upcoming',
            ];
        } else {
            $urls = [
                'https://rest.bandsintown.com/artists/' . urlencode($test_id) . '/events?app_id=limitless_agency&date=upcoming',
                'https://rest.bandsintown.com/artists/' . urlencode($test_id) . '/events?app_id=test&date=upcoming',
                'https://rest.bandsintown.com/artists/' . urlencode($test_id) . '/events?date=upcoming',
                'https://rest.bandsintown.com/artists/' . urlencode($test_id) . '/events?app_id=limitless_agency',
                'https://rest.bandsintown.com/artists/' . urlencode($test_id) . '/events',
            ];
        }

        foreach ($urls as $index => $url) {
        $output .= '<h4>Test ' . ($index + 1) . ': ' . esc_html($url) . '</h4>';

        $response = wp_remote_get($url, array(
            'timeout' => 15,
            'headers' => array(
                'User-Agent' => 'Limitless Agency Website'
            )
        ));

        if (is_wp_error($response)) {
            $output .= '<p style="color: red;">Error: ' . $response->get_error_message() . '</p>';
            continue;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        $output .= '<p><strong>Response Code:</strong> ' . $response_code . '</p>';
        $output .= '<p><strong>Response Body:</strong></p>';
        $output .= '<pre style="background: #f5f5f5; padding: 10px; max-height: 300px; overflow: auto;">' . esc_html($body) . '</pre>';

        if ($response_code === 200) {
            $decoded = json_decode($body, true);
            if ($decoded) {
                $output .= '<p><strong>Decoded JSON:</strong></p>';
                $output .= '<pre style="background: #e8f5e8; padding: 10px; max-height: 300px; overflow: auto;">' . print_r($decoded, true) . '</pre>';
            }
        }

            $output .= '<hr>';
        }

        $output .= '<hr style="border: 2px solid #333; margin: 20px 0;">';
    }

    return $output;
}
add_shortcode('debug_bandsintown', 'debug_bandsintown_api');

// Clear Bandsintown cache (alleen voor admins)
function clear_bandsintown_cache() {
    if (!current_user_can('manage_options')) {
        return 'Access denied';
    }

    global $wpdb;
    $deleted = $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_bandsintown_events_%'");
    $deleted += $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_bandsintown_events_%'");

    return "Cleared $deleted Bandsintown cache entries.";
}
add_shortcode('clear_bandsintown_cache', 'clear_bandsintown_cache');

// Clear Spotify cache (alleen voor admins)
function clear_spotify_cache() {
    if (!current_user_can('manage_options')) {
        return 'Access denied';
    }

    global $wpdb;
    $deleted = $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_spotify_latest_release_%'");
    $deleted += $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_spotify_latest_release_%'");

    // Ook access token cache legen
    $deleted += $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name = '_transient_spotify_access_token'");
    $deleted += $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name = '_transient_timeout_spotify_access_token'");

    return "Cleared $deleted Spotify cache entries.";
}
add_shortcode('clear_spotify_cache', 'clear_spotify_cache');

// Debug Spotify API (alleen voor admins)
function debug_spotify_api($atts) {
    if (!current_user_can('manage_options')) {
        return 'Access denied';
    }

    $atts = shortcode_atts([
        'artist_id' => '30sKm4Zacgq8mC0l7vNmuD' // Default naar jouw artist ID
    ], $atts);

    $artist_id = sanitize_text_field($atts['artist_id']);
    $token = get_spotify_access_token();

    if (!$token) {
        return '<div style="background: #f0f0f0; padding: 20px; margin: 20px 0; border: 1px solid #ccc;"><strong>Error:</strong> Could not get Spotify access token. Check credentials.</div>';
    }

    $output = '<div style="background: #f0f0f0; padding: 20px; margin: 20px 0; border: 1px solid #ccc; font-family: monospace; font-size: 12px;">';
    $output .= '<h3>Spotify API Debug for Artist ID: ' . esc_html($artist_id) . '</h3>';

    // Test de API call
    $url = 'https://api.spotify.com/v1/artists/' . $artist_id . '/albums?include_groups=single,album&limit=50&market=NL';
    $output .= '<p><strong>API URL:</strong> ' . esc_html($url) . '</p>';

    $args = array(
        'headers' => array(
            'Authorization' => 'Bearer ' . $token
        ),
        'timeout' => 15
    );

    $response = wp_remote_get($url, $args);

    if (is_wp_error($response)) {
        $output .= '<p><strong>Error:</strong> ' . esc_html($response->get_error_message()) . '</p>';
    } else {
        $response_code = wp_remote_retrieve_response_code($response);
        $output .= '<p><strong>Response Code:</strong> ' . esc_html($response_code) . '</p>';

        if ($response_code === 200) {
            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (!empty($body['items'])) {
                $output .= '<p><strong>Found Albums:</strong> ' . count($body['items']) . '</p>';

                // Sorteer albums op release datum
                $albums = $body['items'];
                usort($albums, function($a, $b) {
                    return strtotime($b['release_date']) - strtotime($a['release_date']);
                });

                $output .= '<p><strong>Albums (sorted by release date):</strong></p>';
                $output .= '<ul>';
                foreach (array_slice($albums, 0, 10) as $album) { // Toon eerste 10
                    $output .= '<li>' . esc_html($album['name']) . ' (' . esc_html($album['release_date']) . ') - ' . esc_html($album['album_type']) . '</li>';
                }
                $output .= '</ul>';

                $latest = $albums[0];
                $output .= '<p><strong>Latest Release:</strong> ' . esc_html($latest['name']) . ' (' . esc_html($latest['release_date']) . ')</p>';
            } else {
                $output .= '<p><strong>No albums found</strong></p>';
            }
        } else {
            $output .= '<p><strong>API Error:</strong> ' . esc_html(wp_remote_retrieve_body($response)) . '</p>';
        }
    }

    $output .= '</div>';
    return $output;
}
add_shortcode('debug_spotify', 'debug_spotify_api');

// Quick test functie voor Bandsintown API
function quick_bandsintown_test() {
    if (!current_user_can('manage_options')) {
        return 'Access denied';
    }

    $output = '<h3>Quick Bandsintown Tests:</h3>';

    // Test verschillende URLs voor Dual Damage met verschillende app_id's
    $test_urls = [
        'Dual Damage (ID + app_id)' => 'https://rest.bandsintown.com/artists/id_15534961/events?app_id=limitless_agency',
        'Dual Damage (ID + generic app_id)' => 'https://rest.bandsintown.com/artists/id_15534961/events?app_id=your_app_name',
        'Dual Damage (Name + app_id)' => 'https://rest.bandsintown.com/artists/dual-damage/events?app_id=limitless_agency',
        'Skrillex (Control + app_id)' => 'https://rest.bandsintown.com/artists/skrillex/events?app_id=limitless_agency',
        'Test without app_id' => 'https://rest.bandsintown.com/artists/skrillex/events',
    ];

    foreach ($test_urls as $label => $test_url) {
        $response = wp_remote_get($test_url, array(
            'timeout' => 15,
            'headers' => array(
                'User-Agent' => 'Limitless Agency Website'
            )
        ));

        $output .= '<h4>' . esc_html($label) . ':</h4>';
        $output .= '<p><small>URL: ' . esc_html($test_url) . '</small></p>';

        if (is_wp_error($response)) {
            $output .= '<p style="color: red;">Error: ' . $response->get_error_message() . '</p>';
            continue;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        $output .= '<p><strong>Response Code:</strong> ' . $response_code . '</p>';

        if ($response_code === 200) {
            $decoded = json_decode($body, true);
            if ($decoded && is_array($decoded)) {
                $output .= '<p style="color: green;"><strong>Success!</strong> Found ' . count($decoded) . ' events.</p>';
                if (!empty($decoded)) {
                    $output .= '<p><strong>First event:</strong> ' . esc_html($decoded[0]['datetime'] ?? 'No datetime') . ' - ' . esc_html($decoded[0]['venue']['name'] ?? 'No venue') . '</p>';
                }
            } else {
                $output .= '<p><strong>Response Body:</strong> ' . esc_html(substr($body, 0, 300)) . '</p>';
            }
        } else {
            $output .= '<p style="color: red;"><strong>Failed with code:</strong> ' . $response_code . '</p>';
            $output .= '<p><strong>Response:</strong> ' . esc_html(substr($body, 0, 300)) . '</p>';
        }

        $output .= '<hr>';
    }

    return $output;
}
add_shortcode('quick_bandsintown_test', 'quick_bandsintown_test');

// Alternatieve functie: scrape Bandsintown website als API faalt
function scrape_bandsintown_events($artist_id) {
    if (!is_numeric($artist_id)) {
        return false;
    }

    $cache_key = 'bandsintown_scrape_' . md5($artist_id);
    $cached_events = get_transient($cache_key);

    if ($cached_events !== false) {
        return $cached_events;
    }

    // Probeer de artist pagina te scrapen
    $url = 'https://www.bandsintown.com/a/' . urlencode($artist_id);

    $response = wp_remote_get($url, array(
        'timeout' => 15,
        'headers' => array(
            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        )
    ));

    if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
        return false;
    }

    $html = wp_remote_retrieve_body($response);

    // Zoek naar JSON data in de HTML (Bandsintown gebruikt vaak embedded JSON)
    if (preg_match('/window\.__INITIAL_STATE__\s*=\s*({.+?});/', $html, $matches)) {
        $json_data = json_decode($matches[1], true);

        if ($json_data && isset($json_data['events']) && is_array($json_data['events'])) {
            // Cache voor 30 minuten
            set_transient($cache_key, $json_data['events'], 30 * MINUTE_IN_SECONDS);
            return $json_data['events'];
        }
    }

    // Als dat niet werkt, zoek naar event data in script tags
    if (preg_match_all('/<script[^>]*>.*?window\.events\s*=\s*(\[.+?\]);.*?<\/script>/s', $html, $matches)) {
        foreach ($matches[1] as $json_string) {
            $events = json_decode($json_string, true);
            if ($events && is_array($events)) {
                // Cache voor 30 minuten
                set_transient($cache_key, $events, 30 * MINUTE_IN_SECONDS);
                return $events;
            }
        }
    }

    return false;
}

// Test scraping functie
function test_bandsintown_scrape() {
    if (!current_user_can('manage_options')) {
        return 'Access denied';
    }

    $artist_id = '15534961';
    $events = scrape_bandsintown_events($artist_id);

    $output = '<h3>Bandsintown Scraping Test:</h3>';
    $output .= '<p>Testing scraping for artist ID: ' . esc_html($artist_id) . '</p>';

    if ($events === false) {
        $output .= '<p style="color: red;">Scraping failed</p>';
    } elseif (empty($events)) {
        $output .= '<p style="color: orange;">No events found via scraping</p>';
    } else {
        $output .= '<p style="color: green;">Found ' . count($events) . ' events via scraping!</p>';
        $output .= '<pre>' . print_r(array_slice($events, 0, 2), true) . '</pre>';
    }

    return $output;
}
add_shortcode('test_bandsintown_scrape', 'test_bandsintown_scrape');

// Voeg automatisch ACF velden toe voor 'artist' post type
add_action('acf/init', function() {
    if( function_exists('acf_add_local_field_group') ) {
        acf_add_local_field_group(array(
            'key' => 'group_artist_fields',
            'title' => 'Artist Fields',
            'fields' => array(
                array(
                    'key' => 'field_artist_image',
                    'label' => 'Image',
                    'name' => 'image',
                    'type' => 'image',
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                ),
                array(
                    'key' => 'field_artist_slider_image_home',
                    'label' => 'Slider Image Home',
                    'name' => 'slider_image_home',
                    'type' => 'image',
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                ),
                array(
                    'key' => 'field_artist_logo',
                    'label' => 'Logo',
                    'name' => 'logo',
                    'type' => 'image',
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                ),
                array(
                    'key' => 'field_artist_instagram',
                    'label' => 'Instagram',
                    'name' => 'instagram',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_artist_tiktok',
                    'label' => 'Tiktok',
                    'name' => 'tiktok',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_artist_youtube',
                    'label' => 'YouTube',
                    'name' => 'youtube',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_artist_spotify',
                    'label' => 'Spotify',
                    'name' => 'spotify',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_artist_soundcloud',
                    'label' => 'SoundCloud',
                    'name' => 'soundcloud',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_artist_facebook',
                    'label' => 'Facebook',
                    'name' => 'facebook',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_artist_twitter_x',
                    'label' => 'Twitter (X)',
                    'name' => 'twitter_x',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_artist_hardstyle',
                    'label' => 'Hardstyle',
                    'name' => 'hardstyle',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_artist_presskit',
                    'label' => 'Presskit',
                    'name' => 'presskit',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_artist_visual_pack',
                    'label' => 'Visual Pack',
                    'name' => 'visual_pack',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_artist_images',
                    'label' => 'Images (Slider)',
                    'name' => 'images',
                    'type' => 'gallery',
                    'preview_size' => 'medium',
                ),
                array(
                    'key' => 'field_artist_spotify_id',
                    'label' => 'Spotify Artist ID',
                    'name' => 'spotify_id',
                    'type' => 'text',
                ),
                array(
                    'key' => 'field_artist_bandsintown_id',
                    'label' => 'Bandsintown ID',
                    'name' => 'bandsintown_id',
                    'type' => 'text',
                ),
                array(
                    'key' => 'field_artist_youtube_url',
                    'label' => 'YouTube Video URL',
                    'name' => 'youtube_url',
                    'type' => 'url',
                    'instructions' => 'Paste the full YouTube video URL (e.g., https://www.youtube.com/watch?v=tOUzIDgZ28w)',
                    'placeholder' => 'https://www.youtube.com/watch?v=...',
                ),
                array(
                    'key' => 'field_artist_spotify_url',
                    'label' => 'Spotify Playlist/Album URL',
                    'name' => 'spotify_url',
                    'type' => 'url',
                    'instructions' => 'Paste the full Spotify playlist or album URL (e.g., https://open.spotify.com/playlist/...)',
                    'placeholder' => 'https://open.spotify.com/playlist/...',
                ),
                array(
                    'key' => 'field_artist_description',
                    'label' => 'Description',
                    'name' => 'description',
                    'type' => 'wysiwyg',
                    'media_upload' => false, // optioneel
                ),
                array(
                    'key' => 'field_artist_category',
                    'label' => 'Category',
                    'name' => 'category',
                    'type' => 'text',
                ),
                array(
                    'key' => 'field_artist_managers',
                    'label' => 'Artist Manager',
                    'name' => 'artist_managers',
                    'type' => 'post_object',
                    'post_type' => array('team'),
                    'multiple' => 1,
                    'return_format' => 'id',
                    'ui' => 1,
                    'filter_by' => array('category'), // optioneel, alleen als je wilt filteren op category
                    'instructions' => 'Selecteer een of meerdere artist manager voor deze artiest.',
                ),
                array(
                    'key' => 'manager_mail_link',
                    'label' => 'Direct mail link to artist manager (optional)',
                    'name' => 'manager_mail_link',
                    'type' => 'text',
                ),
                array(
                    'key' => 'field_artist_roster_type',
                    'label' => 'Roster Type',
                    'name' => 'roster_type',
                    'type' => 'select',
                    'choices' => array(
                        'main' => 'Main Roster',
                        'ones_to_watch' => 'Ones to watch',
                    ),
                    'return_format' => 'value',
                    'ui' => 1,
                    'instructions' => 'Selecteer of deze artiest bij het Main Roster of Ones to watch hoort.',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'artist',
                    ),
                ),
            ),
        ));
    }
});

// Custom Post Type: Team
function register_team_post_type() {
    $labels = array(
        'name'               => _x('Team', 'post type general name', 'textdomain'),
        'singular_name'      => _x('Team Member', 'post type singular name', 'textdomain'),
        'menu_name'          => _x('Team', 'admin menu', 'textdomain'),
        'name_admin_bar'     => _x('Team Member', 'add new on admin bar', 'textdomain'),
        'add_new'            => _x('Add New', 'team member', 'textdomain'),
        'add_new_item'       => __('Add New Team Member', 'textdomain'),
        'new_item'           => __('New Team Member', 'textdomain'),
        'edit_item'          => __('Edit Team Member', 'textdomain'),
        'view_item'          => __('View Team Member', 'textdomain'),
        'all_items'          => __('All Team Members', 'textdomain'),
        'search_items'       => __('Search Team Members', 'textdomain'),
        'parent_item_colon'  => __('Parent Team Members:', 'textdomain'),
        'not_found'          => __('No team members found.', 'textdomain'),
        'not_found_in_trash' => __('No team members found in Trash.', 'textdomain')
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'team'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => null,
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt'),
        'show_in_rest'       => true,
        'menu_icon'          => 'dashicons-businessperson', // Icoon voor teamleden
    );

    register_post_type('team', $args);
}
add_action('init', 'register_team_post_type');

// Voeg automatisch ACF velden toe voor 'team' post type
add_action('acf/init', function() {
    if( function_exists('acf_add_local_field_group') ) {
        acf_add_local_field_group(array(
            'key' => 'group_team_fields',
            'title' => 'Team Fields',
            'fields' => array(
                array(
                    'key' => 'field_team_photo',
                    'label' => 'Picture',
                    'name' => 'photo',
                    'type' => 'image',
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                ),
                array(
                    'key' => 'field_team_categories',
                    'label' => 'Categories',
                    'name' => 'categories',
                    'type' => 'select',
                    'ui' => 1,
                    'instructions' => 'Selecteer een of meerdere categorieën voor dit teamlid.',
                    'choices' => array(
                        'Management' => 'Management',
                        'Agents' => 'Agents',
                        'Artist Manager' => 'Artist Manager',
                    ),
                    'return_format' => 'value',
                    'multiple' => 1,
                ),
                array(
                    'key' => 'field_team_function',
                    'label' => 'Function',
                    'name' => 'function',
                    'type' => 'text',
                ),
                array(
                    'key' => 'field_team_phone',
                    'label' => 'Telefoonnummer',
                    'name' => 'phone',
                    'type' => 'text',
                ),
                array(
                    'key' => 'field_team_email',
                    'label' => 'E-mailadres',
                    'name' => 'email',
                    'type' => 'email',
                ),
                array(
                    'key' => 'field_team_whatsapp',
                    'label' => 'WhatsApp Link',
                    'name' => 'whatsapp',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_team_instagram',
                    'label' => 'Instagram Link',
                    'name' => 'instagram',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_team_tiktok',
                    'label' => 'Tiktok Link',
                    'name' => 'tiktok',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_team_facebook',
                    'label' => 'Facebook Link',
                    'name' => 'facebook',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_team_linkedin',
                    'label' => 'LinkedIn Link',
                    'name' => 'linkedin',
                    'type' => 'url',
                ),
                array(
                    'key' => 'field_team_website',
                    'label' => 'Website',
                    'name' => 'website',
                    'type' => 'url',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'team',
                    ),
                ),
            ),
        ));
    }
});



function get_spotify_access_token() {
    // Zet hier je Spotify Client ID en Secret
    $client_id = defined('SPOTIFY_CLIENT_ID') ? SPOTIFY_CLIENT_ID : '';
    $client_secret = defined('SPOTIFY_CLIENT_SECRET') ? SPOTIFY_CLIENT_SECRET : '';
    if (!$client_id || !$client_secret) return '';

    $transient_key = 'spotify_access_token';
    $token = get_transient($transient_key);
    if ($token) return $token;

    $response = wp_remote_post('https://accounts.spotify.com/api/token', array(
        'body' => array(
            'grant_type' => 'client_credentials',
        ),
        'headers' => array(
            'Authorization' => 'Basic ' . base64_encode($client_id . ':' . $client_secret),
        ),
    ));
    if (is_wp_error($response)) return '';
    $body = json_decode(wp_remote_retrieve_body($response), true);
    if (!empty($body['access_token'])) {
        set_transient($transient_key, $body['access_token'], intval($body['expires_in']) - 60);
        return $body['access_token'];
    }
    return '';
}

add_filter('artist_spotify_token', function($token) {
    return get_spotify_access_token();
});

